{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@formatjs/intl-localematcher": "^0.5.4", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@juggle/resize-observer": "^3.4.0", "@lemonsqueezy/lemonsqueezy.js": "^2.0.0", "@next/third-parties": "^14.1.0", "@reduxjs/toolkit": "^2.0.1", "@sentry/nextjs": "^7.101.1", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@types/react-toastify": "^4.1.0", "add": "^2.0.6", "autoprefixer": "10.4.15", "axios": "^1.6.5", "citation-js": "^0.7.8", "file-saver": "^2.0.5", "geist": "^1.0.0", "i": "^0.3.7", "i18next": "^23.8.2", "i18next-browser-languagedetector": "^7.2.0", "i18next-resources-to-backend": "^1.2.0", "lodash": "^4.17.21", "negotiator": "^0.6.3", "next": "latest", "next-redux-wrapper": "^8.1.0", "openai": "^4.24.3", "postcss": "8.4.35", "punycode": "^2.3.1", "quill": "^1.3.7", "quill-to-word": "^1.3.0", "raw-body": "^2.5.2", "react": "^18.2.0", "react-cookie": "^7.0.2", "react-dom": "^18.2.0", "react-i18next": "^14.0.5", "react-icons": "^5.0.1", "react-redux": "^9.1.0", "react-toastify": "^10.0.4", "react-transition-group": "^4.4.5", "react-use": "^17.4.3", "redux": "^5.0.1", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "sweetalert2": "^11.10.6", "tailwindcss": "3.3.3", "typescript": "5.1.3", "xml2js": "^0.6.2"}, "devDependencies": {"@next/bundle-analyzer": "^14.1.0", "@types/file-saver": "^2.0.7", "@types/negotiator": "^0.6.3", "@types/node": "^20.3.1", "@types/react": "^18.2.48", "@types/react-dom": "18.2.5", "@types/react-transition-group": "^4.4.10", "@types/redux-logger": "^3.0.12", "encoding": "^0.1.13"}, "version": "1.9.0"}