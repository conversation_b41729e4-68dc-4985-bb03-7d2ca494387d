{"give me a star in GitHub": " give me a star in GitHub", "更新索引": "update paper reference index", "AI写作": "AI writing", "Paper2AI": "Paper2AI", "点击AI写作就是正常的对话交流，点击寻找文献会根据输入的主题词去寻找对应论文": "Click AI Write for normal conversation, click Paper2AI to find corresponding papers based on the input topic", "选择论文来源": "Select the source of the paper", "选择AI模型": "Select AI model", "生成轮数": "Generation Rounds", "时间范围": "Range of literature release dates, from this time to this year", "更新文中的上标，使得数字顺序排列": "Update the superscript in the text to make the numbers in order", "停止生成": "Stop Generation", "+ Add Paper": "+ Add Paper", "Buy VIP TO UNLOCK Cloud Sync and Edit Mutiple Papers Simultaneously": "Buy VIP TO UNLOCK Cloud Sync and Edit Mutiple Papers Simultaneously", "Paper Management": "Paper Management", "Your Cloud Papers": "Your Cloud Papers", "复制": "Copy", "添加自定义引用": "Add Custom Reference", "复制所有引用": "Copy All References", "删除所有引用": "Delete All References", "Title": "Title", "Author": "Author", "Year": "Year", "Publisher": "Publisher", "Url": "Url", "配置选择器": "Configure Selector", "Upstream URL:": "Upstream URL:", "System Prompt(Paper2AI):": "System Prompt(Paper2AI):", "configurations": {"cocopilot-gpt4": "cocopilot-gpt4 (apiKey prefix with ghu, as GitHub does not allow uploading complete keys)", "deepseek-chat": "deepseek-chat (Model needs to be manually changed to this one)", "caifree": "ca<PERSON><PERSON> (Recommended)", "linuxdo": "linuxdo", "coze": "coze", "vv佬": "vv giant(Recommended)", "官网反代": "Official website reverse proxy", "蒙恬大将军": "Mengtian General(Recommended)", "oneapi": "oneapi", "custom": "Custom"}, "鼠标点击段落中的上标跳转到文献引用？": "Click the superscript in the paragraph to jump to the reference?", "是否检查文献与主题相关性(如果不相关则不会传给AI引用)": "Check the relevance of the literature to the topic (if it is not relevant, it will not be passed to the AI reference)"}