# Changelog

## [1.9.0](https://www.github.com/14790897/paper-ai/compare/v1.8.0...v1.9.0) (2024-03-13)


### Features

* google登录 ([15c3a1f](https://www.github.com/14790897/paper-ai/commit/15c3a1f0acae8f5d6e610382b4fb886cc637d4f9))
* 允许重置密码 ([939f5c2](https://www.github.com/14790897/paper-ai/commit/939f5c28e9f658f2899cb262c16e78d42601a320))
* 可以手动停止AI的输出(左下角按钮) ([a72329d](https://www.github.com/14790897/paper-ai/commit/a72329d4a209aba4a0111093ece3b8cf89113ad2))
* 完成linuxdo oauth ([0090ffd](https://www.github.com/14790897/paper-ai/commit/0090ffd3bbe83a99a4de15e733619569cd78a69b))


### Bug Fixes

* GitHub登入可以插入信息 ([559b401](https://www.github.com/14790897/paper-ai/commit/559b4010c276f45dec791aaea17133d6690aac63))
* github登录无法将用户数据插入数据库 ([65db119](https://www.github.com/14790897/paper-ai/commit/65db119a9ac97009f15238e6ef26236bc6be14c7))
* quilleditor ssr加载失败 ([8a25248](https://www.github.com/14790897/paper-ai/commit/8a25248c29c578c75ec9e05fd1cdd339f131e5d6))
* remove .env.local ([0bfaacf](https://www.github.com/14790897/paper-ai/commit/0bfaacf6ee52309aaa55961037aba29a1558e6a6))
* remove freshwork ([f8e4cfd](https://www.github.com/14790897/paper-ai/commit/f8e4cfd205cfb5b5ec105f662c0ff0b6d8590429))
* seo ([72300bf](https://www.github.com/14790897/paper-ai/commit/72300bf6eb23757845734ccfcfb99d40274c9257))
* seo图片问题 ([6e807a7](https://www.github.com/14790897/paper-ai/commit/6e807a703d2a72f6ce557f043abefc44d746992c))
* 使用cf反代解决semantic cors问题 ([836aa49](https://www.github.com/14790897/paper-ai/commit/836aa49847ec7bc4fd2686a324a82ab19173fc83))
* 前端环境变量需要使用NEXT_PUBLIC_ ([6243aa5](https://www.github.com/14790897/paper-ai/commit/6243aa5401f7689aa7e4caf81a39a87578484299))
* 应用id问题 ([64cab48](https://www.github.com/14790897/paper-ai/commit/64cab48ae34ebb5500553dd3908167a95f1cbaf4))
* 新用户没有获得编辑器焦点会导致报错 ([4b64827](https://www.github.com/14790897/paper-ai/commit/4b64827c1d3c77dc05ebad359f0f4d384145211f))
* 点击空白页面可以可以取消论文列表页 ([b5bd878](https://www.github.com/14790897/paper-ai/commit/b5bd878cdafb6a54826f8aeafb16e6e6bc1e95bb))
* 读取ai响应 ([e7aa998](https://www.github.com/14790897/paper-ai/commit/e7aa998ca7dafe38a0c12ada4f168717c6e45439))
* 谷歌登陆后不再弹出 ([5f3252d](https://www.github.com/14790897/paper-ai/commit/5f3252da6e2715e4afbb7ab0b648112c22604230))
* 通过判断user是否登陆来决定是否one tap ([8730415](https://www.github.com/14790897/paper-ai/commit/87304153526200a2c3340c433c87348d5199287b))

## [1.8.0](https://www.github.com/14790897/paper-ai/compare/v1.7.0...v1.8.0) (2024-02-24)


### Features

* 优雅的报错提示 ([93f8889](https://www.github.com/14790897/paper-ai/commit/93f8889c5798b6f47dfb3a3831c051a11078786d))
* 可以切换多种引用格式 ([9b835bb](https://www.github.com/14790897/paper-ai/commit/9b835bbadd89763ab63fa159f35dd7ed657296c6))
* 增加了时间范围选择,除arxiv ([49a757f](https://www.github.com/14790897/paper-ai/commit/49a757f6c7d2a18bbde0e95cf57d4aaf5127e24c))
* 论文搜索完成进行提示 ([6c37459](https://www.github.com/14790897/paper-ai/commit/6c37459fe3b35f379e5b0b4d3c65224f32efb04d))


### Bug Fixes

* 复制文献问题 ([a38b9ce](https://www.github.com/14790897/paper-ai/commit/a38b9cee529a04100c4ebb8ba4129c2caa86f2d6))

## [1.7.0](https://www.github.com/14790897/paper-ai/compare/v1.6.0...v1.7.0) (2024-02-22)


### Features

* pwa可离线访问 service worker ([ff09eee](https://www.github.com/14790897/paper-ai/commit/ff09eee99343cfa6ee6da301428c4a0b3c790bf6))
* seo优化 ([bedc8a3](https://www.github.com/14790897/paper-ai/commit/bedc8a3ce0a314fd2cc7add65defdce372a1c432))
* 加了个用户反馈组件 ([cef12f3](https://www.github.com/14790897/paper-ai/commit/cef12f31a026be9ccc6229943f6a64b3ebf930db))
* 可选的对文献相关性检验 ([6cda6d1](https://www.github.com/14790897/paper-ai/commit/6cda6d176aac29b3972dfde305a62c0be0dc2437))


### Bug Fixes

* 用户反馈组件 ([8155299](https://www.github.com/14790897/paper-ai/commit/81552993bed272a5a05b19edd41aeb10106124b8))

## [1.6.0](https://www.github.com/14790897/paper-ai/compare/v1.5.0...v1.6.0) (2024-02-19)


### Features

* github登录（测试中） ([4ee0816](https://www.github.com/14790897/paper-ai/commit/4ee08169df05a9bcf487a25c4b56c3785edbea7a))
* 可以进行多轮文献查询 ([9d799f1](https://www.github.com/14790897/paper-ai/commit/9d799f1736a7ff72e09f9e36e916c0ab9e04cec4))
* 增加了一个显示当前任务进度的进度条 ([c55a93c](https://www.github.com/14790897/paper-ai/commit/c55a93c79b20c7fb4b5ff15027110267d6874c24))
* 如果AI多次引用同一文献则只返回第一个文献的引用数字 ([763a106](https://www.github.com/14790897/paper-ai/commit/763a1062f982b5f0b96da5afb3b3bb96fd66eaef))


### Bug Fixes

* ai对话 ([96d780d](https://www.github.com/14790897/paper-ai/commit/96d780dd3a1c04e4c4adbd61cd87d07a42fa3eaa))
* pubmed参数写反 ([07070bf](https://www.github.com/14790897/paper-ai/commit/07070bf253044958931e138b5af0099ddc3fb8dd))
* translation ([03febb1](https://www.github.com/14790897/paper-ai/commit/03febb136415fcd48a4248d19c1086bfc0c95f8d))
* 修复lemon路由 ([5e7afea](https://www.github.com/14790897/paper-ai/commit/5e7afea400af03716d08e2d6d7aadb6eccd3448e))
* 去除暗色模式 ([da5cb4f](https://www.github.com/14790897/paper-ai/commit/da5cb4fac84e800e4e8bcc105a395088af615631))
* 只有在AI返回的内容没有错之后才能添加文献 ([3040c11](https://www.github.com/14790897/paper-ai/commit/3040c11ea1b843f923475ac117516e82de3f4458))
* 图标正常显示 ([8ab1bdd](https://www.github.com/14790897/paper-ai/commit/8ab1bdd3935713595d35ed41471146cfa83cc4f0))
* 导出word时文献不更新，因为usecallback没有添加变量 ([01d3ffe](https://www.github.com/14790897/paper-ai/commit/01d3ffebf7a86c27161d97b73a949a1f0d73a62b))
* 论文引用格式 ([b603365](https://www.github.com/14790897/paper-ai/commit/b603365265314f61122a8bab0da1561bc1c0c5a2))

## [1.5.0](https://www.github.com/14790897/paper-ai/compare/v1.4.0...v1.5.0) (2024-02-13)


### Features

* pubmed论文格式更新 ([3636fe1](https://www.github.com/14790897/paper-ai/commit/3636fe1c358e3f4edebca2c17f527383f787e7a4))
* 加了一个按钮用来控制鼠标点击段落中的上标跳转到文献引用 ([e5d267c](https://www.github.com/14790897/paper-ai/commit/e5d267cf7075d8830bee172d2f8e5b6b1f487487))
* 更准确的文献引用 ([c4d4100](https://www.github.com/14790897/paper-ai/commit/c4d410073caea3cdc7e016e492b62c22fec99543))
* 记录selection位置以及自动清空UserInput内容 ([9e85552](https://www.github.com/14790897/paper-ai/commit/9e85552d8966f7408e2ae61b92b91d0b3ae40e59))


### Bug Fixes

* 删除段落的错误 ([089a7af](https://www.github.com/14790897/paper-ai/commit/089a7afa7e84ccd965e9a1d51a2cae47c0269b3e))
* 未选中焦点的时候会报错 ([342d78b](https://www.github.com/14790897/paper-ai/commit/342d78bd882367cbf9afbfb234f85bfb05e3d489))

## [1.4.0](https://www.github.com/14790897/paper-ai/compare/v1.3.0...v1.4.0) (2024-02-12)


### Features

* i18n 切换 ([2573950](https://www.github.com/14790897/paper-ai/commit/2573950ad82140a16d1d8d6c48d33fcfc269d81e))
* i18n中文英文 ([d64295e](https://www.github.com/14790897/paper-ai/commit/d64295e27ad3668539be8cb3b8b46bbccf086334))
* sentry ([f5ee326](https://www.github.com/14790897/paper-ai/commit/f5ee32669c23ceb79ea618dd04a8e781eabb4936))
* sentry追踪用户 ([b983427](https://www.github.com/14790897/paper-ai/commit/b983427721dda54aac187c9b95f8d21988944c06))


### Bug Fixes

* reference中的journal终于搞定 ([67212d6](https://www.github.com/14790897/paper-ai/commit/67212d6a8514c67d8d9e19733ba1d228c0690eed))

## [1.3.0](https://www.github.com/14790897/paper-ai/compare/v1.2.0...v1.3.0) (2024-02-10)


### Features

* lemonsqueezy1 ([fbd899c](https://www.github.com/14790897/paper-ai/commit/fbd899cae3d2a1daf41e3578a5b04d258da42b99))
* supa utils ([e7567cb](https://www.github.com/14790897/paper-ai/commit/e7567cb49d132059b03d7296e402d40a287cff31))
* supa路由 ([20add6b](https://www.github.com/14790897/paper-ai/commit/20add6b617cc9d205299f4ca02758b0ac55639ad))
* vip按钮 ([aeff960](https://www.github.com/14790897/paper-ai/commit/aeff96032ef4fa5d20ee62ee6a0813778095c726))
* 使用免费的deepseek ([10e7ef0](https://www.github.com/14790897/paper-ai/commit/10e7ef05c97bb3faff1563995b460defed72f1cb))
* 删除引用的时候同时删除段落 ([65da583](https://www.github.com/14790897/paper-ai/commit/65da583258346d8259707eb828d61b4e7790ec48))
* 在设置界面保存了多个可用的配置 ([a723425](https://www.github.com/14790897/paper-ai/commit/a72342504fc4b10498258c84a5fad812a32dee04))
* 完善了错误处理 ([91ab703](https://www.github.com/14790897/paper-ai/commit/91ab703708979e7c4f4bbc64793274db9e6c01bf))
* 导出word功能增加导出文献内容 ([8dcb54a](https://www.github.com/14790897/paper-ai/commit/8dcb54a49b46aed8441e28aeaf3e4489d9bae61a))
* 尝试删除索引的时候删除整个段落 ([732dd73](https://www.github.com/14790897/paper-ai/commit/732dd738c93601a0cd81379ced2dfa7ddd8ce683))
* 引用和内容分开更新 ([0276ff8](https://www.github.com/14790897/paper-ai/commit/0276ff8964486c89519e37265adbab5072e6c1aa))
* 注册时在profiles插入用户信息，方便设置vip时读取email操作 ([8629d20](https://www.github.com/14790897/paper-ai/commit/8629d2034112165c16deb8f3f50f5f43899d1cd2))
* 点击引用数字调转到对应文献 ([3580c34](https://www.github.com/14790897/paper-ai/commit/3580c34e830d121a702a7cdbfaa5ed3a3c7a44bc))
* 管理云端多篇论文功能 ([fe31198](https://www.github.com/14790897/paper-ai/commit/fe31198124f9459c579260018ba673a1353b077f))
* 编辑过程中同步云端 ([2ca6d3d](https://www.github.com/14790897/paper-ai/commit/2ca6d3d212861aa6b54ac6beddd1e498026631ce))
* 自动识别最近的文献序号在那之后插入新的文献 ([ba8722a](https://www.github.com/14790897/paper-ai/commit/ba8722afdaf5698732521c1ad5be1ab8039a1655))


### Bug Fixes

* AI输入的时候鼠标可以失去焦点 ([88063ba](https://www.github.com/14790897/paper-ai/commit/88063baa2ed07ebb807b21138054a9805d948da0))
* isVip改为redux状态 ([91e9759](https://www.github.com/14790897/paper-ai/commit/91e9759cb7192901e88dd72699b33caad066a8ac))
* lemon ([6e6cf16](https://www.github.com/14790897/paper-ai/commit/6e6cf16fbadafc9a990df8eac8d9f14d8a67fca7))
* lemon ([acae014](https://www.github.com/14790897/paper-ai/commit/acae014a46ee8f3c32e12f5da820145ba8090eae))
* redux ([4fa7796](https://www.github.com/14790897/paper-ai/commit/4fa779698ec308dda603f54cb29cd718b5df41af))
* settings的redux ([9ee43eb](https://www.github.com/14790897/paper-ai/commit/9ee43ebd061498d1a03c14e6adef78840195bfd8))
* settings的redux ([1d4fbaf](https://www.github.com/14790897/paper-ai/commit/1d4fbaf8e426762b1b80f0d8d4761e835b8ac5da))
* system prompt可以输入 ([02357cc](https://www.github.com/14790897/paper-ai/commit/02357cc03661a0cda62413deaf07153cc117ceed))
* vercel部署密钥问题 ([84e0363](https://www.github.com/14790897/paper-ai/commit/84e0363313487020b97d0056c65f4a26f10f4cae))
* 修复pubmed articleUrl 无法正常获取的问题 ([f5ae3c1](https://www.github.com/14790897/paper-ai/commit/f5ae3c1ff456bdb6131a8c39b1d04d0ee2094db7))
* 修复service-role无法访问 ([9cb214d](https://www.github.com/14790897/paper-ai/commit/9cb214d67f7453a6c08d643957996a5ffa3b1110))
* 修复service-role无法访问 ([d0e6a72](https://www.github.com/14790897/paper-ai/commit/d0e6a72f0d57b3ad27e47676556acff8be13debf))
* 刷新之后不获取云端文献引用 ([74486f9](https://www.github.com/14790897/paper-ai/commit/74486f95c2f95b5e1cc6e031e0dddff52fbca15e))
* 将输入栏变得方便 ([165f189](https://www.github.com/14790897/paper-ai/commit/165f189efa7bd0003dd2a35b6acbcd040961198f))
* 引用改变的时候立刻同步 ([01d703a](https://www.github.com/14790897/paper-ai/commit/01d703a17b2b18c7bfead1080426f8f8d3619a36))
* 插入前先换行 ([d56f427](https://www.github.com/14790897/paper-ai/commit/d56f427484d342e893c9ff104f06ddb64e14f145))
* 撤销 ([f761c35](https://www.github.com/14790897/paper-ai/commit/f761c357ea3bf74a11a890e9da942db7c4e7fd4a))
* 文献引用删除修复 ([4e6f628](https://www.github.com/14790897/paper-ai/commit/4e6f628289063f28768bd05f92d23895c7417a27))
* 自动保存编辑器内容 ([6107267](https://www.github.com/14790897/paper-ai/commit/610726712366ca66c7392560f32000cf7e63a87f))
* 解决vip状态不正确，因为没有使用dispatch ([833e2e1](https://www.github.com/14790897/paper-ai/commit/833e2e1b0ec0aac46d7759ac44172432fa31a6f0))
* 解决向云端同步的时候文章内容不更新 ([2286a48](https://www.github.com/14790897/paper-ai/commit/2286a48fc040c972d65f8c2a15c4701d31658869))
* 解决因为闭包导致paperNumberRedux 不正确的问题 ([486c75d](https://www.github.com/14790897/paper-ai/commit/486c75d4d7a9a016399170274bd55bab00f2c3b6))

## [1.2.0](https://www.github.com/14790897/paper-ai/compare/v1.1.0...v1.2.0) (2024-01-29)


### Features

* 上下移动文献功能 ([c7cef37](https://www.github.com/14790897/paper-ai/commit/c7cef370d0568c7bc1a4df798e624bc4494344d2))
* 导出到word功能，避免样式丢失 ([cc2856c](https://www.github.com/14790897/paper-ai/commit/cc2856ceb21532fa1bd8d36b78b028fd627aa726))
* 系统提示自定义 ([c160d3e](https://www.github.com/14790897/paper-ai/commit/c160d3e6af970911e0f0163e0ab2979bdf79b8ad))

## [1.1.0](https://www.github.com/14790897/paper-ai/compare/v1.0.0...v1.1.0) (2024-01-28)


### Features

* pubmed ([00b777b](https://www.github.com/14790897/paper-ai/commit/00b777b634c4ff04dbc7e6ad51e16767a366481e))
* pubmed获取论文 ([98f10ff](https://www.github.com/14790897/paper-ai/commit/98f10ff10a02767993137feebd322e2e8df3dc0e))
* 允许自定义URL并且解决了一部分pubmed跨域问题 ([cab4f0b](https://www.github.com/14790897/paper-ai/commit/cab4f0bf0123569f8fa4e98d9a0ca32a4c8c9547))


### Bug Fixes

* 403 ([8bbe3b3](https://www.github.com/14790897/paper-ai/commit/8bbe3b3646f9b83ac5383f1b82ea1e5195d7d7d4))
* 403 ([0ef25ab](https://www.github.com/14790897/paper-ai/commit/0ef25abfc0294f202ef8b9237d20ae6a4eae7cbb))
* 403 ([7677f74](https://www.github.com/14790897/paper-ai/commit/7677f745e60887974dbe997f1c672936064f4c7d))
* 403 ([c45c1ec](https://www.github.com/14790897/paper-ai/commit/c45c1eca7f22e12659a7c5880149c56fa2615bae))
* arxiv from https ([167372d](https://www.github.com/14790897/paper-ai/commit/167372d93cfe1408254261087acb4e739a53480c))
* content length ([743bbf5](https://www.github.com/14790897/paper-ai/commit/743bbf56d6edf5048230baf2e8d0742dfab72034))
* export ([aed01ac](https://www.github.com/14790897/paper-ai/commit/aed01ac80ce3ed736da1df10db93310554555cb8))
* path ([741c21c](https://www.github.com/14790897/paper-ai/commit/741c21c99921f5d6d4dc0ec7d54c0c16d66d45ff))
* supabase ([ed7f305](https://www.github.com/14790897/paper-ai/commit/ed7f3052f7fabd8409d277fc820aaf69bbb5597c))
* vercel 403 ([8c79cdb](https://www.github.com/14790897/paper-ai/commit/8c79cdb43bfd741dc6365cef5aad16308c902747))
* vercel 403 ([fc24794](https://www.github.com/14790897/paper-ai/commit/fc24794874393057217cd58aa2f7114abd8bf153))
* vercel 403 ([ce6590d](https://www.github.com/14790897/paper-ai/commit/ce6590d1f8e6cffac7b469dae29e8eed6ba7580b))
* vercel 403 ([013754d](https://www.github.com/14790897/paper-ai/commit/013754d35168b022a2e47caefe4d0f23703ff897))
* vercel 403 ([45bf2ae](https://www.github.com/14790897/paper-ai/commit/45bf2aee788d401c3152eba1faca48f2a835b4ce))
* whitelist ([38a9bdf](https://www.github.com/14790897/paper-ai/commit/38a9bdf78a0d25f79ae47eb864e8c361935c4fe3))
* 正确的流失响应处理 ([5ed1dcd](https://www.github.com/14790897/paper-ai/commit/5ed1dcdb51d01b36373d64cdfb5285eebee774be))
* 生产环境变量 ([dc53e28](https://www.github.com/14790897/paper-ai/commit/dc53e286989dadae808cc88ca57d27efe4c330d7))
* 调整pubmed时间 ([8508a35](https://www.github.com/14790897/paper-ai/commit/8508a352520c2328da1e77b0525bdd17941c7c1c))

## 1.0.0 (2024-01-23)


### Features

* release-please ([6c8bfe3](https://www.github.com/14790897/paper-ai/commit/6c8bfe3e7cd43fdc08b4933f7b3dd4a7c68abf80))
* 与ai直接交流 ([cd5b42b](https://www.github.com/14790897/paper-ai/commit/cd5b42bed67b32918458d4815d8f5aabf71743a6))
* 删除文献时，更新索引 ([82149df](https://www.github.com/14790897/paper-ai/commit/82149dfa5d4a394e6db7685d73f79cd3d4120846))
* 点击别的按钮之后可以自动回到之前的光标位置 ([f76205e](https://www.github.com/14790897/paper-ai/commit/f76205ec8fe77abecadc24396e621a8f3dc6b6fc))


### Bug Fixes

* deploy button and env ([437d516](https://www.github.com/14790897/paper-ai/commit/437d5169cb9d4fdbc6ebd22cf84cf5ddcceb8a71))
* editorcontent to redux ([6382683](https://www.github.com/14790897/paper-ai/commit/6382683f91dcbeb3cad3293f9be5ec6705f457b3))
* form里面的按钮必须要指定type不为submit才可以正常 ([64d307b](https://www.github.com/14790897/paper-ai/commit/64d307bbdc2eeb41b83ebd0d52cef15955e45bc4))
* ignore lint ([bc65d1e](https://www.github.com/14790897/paper-ai/commit/bc65d1e11c597133bc492c2f6928133c6b4215b8))
* proxy ([50124d3](https://www.github.com/14790897/paper-ai/commit/50124d35bd170f9e996f403fe00a89d570c2be1f))
* referencesRedux自动存储 ([b4a522b](https://www.github.com/14790897/paper-ai/commit/b4a522b3b8bffcbe718e4d60bbdfc73cf41024a1))
* style ([eaa6561](https://www.github.com/14790897/paper-ai/commit/eaa65616c22451a085080cee7713ef3817483dae))
* 使用redux完成对references的编辑 ([945a98b](https://www.github.com/14790897/paper-ai/commit/945a98b2ed97640b6c95e0c316829b415826b306))
