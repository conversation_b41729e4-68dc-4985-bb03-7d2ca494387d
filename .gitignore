# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
# .env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

pass
.env
# .env.local

.vercel
post.md

# Sentry Config File
.sentryclirc

#vitepress
docs/.vitepress/dist/*
node_modules
dist
cache
# Sentry Config File
.sentryclirc
