@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 200 20% 98%;
    --btn-background: 200 10% 91%;
    --btn-background-hover: 200 10% 89%;
    --foreground: 200 50% 3%;
  }

  /* @media (prefers-color-scheme: dark) {
    :root {
      --background: 200 50% 3%;
      --btn-background: 200 10% 9%;
      --btn-background-hover: 200 10% 12%;
      --foreground: 200 20% 96%;
    }
  } */
}

@layer base {
  * {
    @apply border-foreground/20;
  }
}

.animate-in {
  animation: animateIn 0.3s ease 0.15s both;
}

@keyframes animateIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-in-right {
  animation: slideInFromRight 0.5s ease-out forwards;
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%); /* 从右侧外开始 */
  }
  100% {
    transform: translateX(0); /* 完全进入视图 */
  }
}

.component-container {
  @apply fixed top-1/4 right-0;
  transform: translateX(100%); /* 动画开始前，确保组件位于视图右侧之外 */
}
/* 想给上标添加一个鼠标放上去变手型的效果 */
.ql-editor .ql-super {
  cursor: pointer;
}

@keyframes flash {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.vip-icon {
  animation: flash 1s linear infinite;
}

/* 动画的基本样式 */
.slide-enter {
  opacity: 0;
  transform: translateY(100%); /* 从底部滑入 */
}
.slide-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}

.slide-exit {
  opacity: 1;
}
.slide-exit-active {
  opacity: 0;
  transform: translateY(100%); /* 向底部滑出 */
  transition: opacity 300ms ease-in, transform 300ms ease-in;
}

.paper-management-container {
  position: fixed; /* 或者使用 `absolute` 根据需要 */
  top: 50%; /* 调整到视口的垂直中心 */
  left: 50%; /* 调整到视口的水平中心 */
  background-color: rgba(255, 255, 255, 0.5);
  transform: translate(
    -50%,
    -50%
  ); /* 从中心点向上和向左偏移自身的50%，确保组件居中 */
  z-index: 1000; /* 确保悬浮层在其他内容之上 */
  /* 可以添加其他样式来美化组件，如背景色、阴影等 */
}

#editor {
  /* width: calc(100vw - 20px); */
  min-height: 250px;
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ccc;
}

/* 适配手机样式 */
@media (max-width: 768px) {
  #editor {
    width: 100%; /* 适应屏幕宽度 */
    min-height: 200px; /* 调整为更适合移动设备的尺寸 */
  }

  #Qtoolbar {
    display: flex; /* 使用弹性盒布局 */
    flex-direction: row; /* 项目水平排列 */
    flex-wrap: wrap; /* 允许项目换行 */
    align-items: center; /* 项目在交叉轴上居中对齐 */
    justify-content: center; /* 项目在主轴上靠左对齐 */
  }
  #Qtoolbar > textarea {
    width: 100%;
    min-height: 60px; /* 调整输入框的高度 */
  }
  #Qtoolbar > button,
  #Qtoolbar > select,
  #Qtoolbar > input {
    margin-bottom: 10px; /* 增加元素之间的间距 */
    margin-right: 10px; /* 右边距，增加元素之间的间距 */
  }
}

/* 输入框基本样式 */
.textarea-focus-expand {
  height: 50px; /* 默认高度 */
  flex-grow: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 10px 15px;
  margin-right: 8px;
  color: #333;
  transition: height 0.3s ease; /* 平滑过渡效果 */
}

/* 输入框获得焦点时的样式 */
.textarea-focus-expand:focus {
  height: 100px; /* 聚焦时的高度 */
  border-color: #007bff; /* 改变边框颜色以提供视觉反馈 */
}

.icon-hover:hover {
  transform: scale(1.2); /* 放大到原大小的1.2倍 */
  transition: transform 0.3s ease; /* 平滑过渡效果 */
}

/* 去除toast的最大宽度限制 */
.Toastify__toast-container--top-center:has(div.toastDetail) {
  width: 80%;
}
