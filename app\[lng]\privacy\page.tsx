import React from "react";

export default function PrivacyPolicy() {
  return (
    <div className="max-w-4xl mx-auto p-4">
      <h1>应用隐私政策</h1>
      <p>
        欢迎使用paperai！我们致力于保护您的个人信息和隐私。本隐私政策旨在帮助您了解我们如何收集、使用、存储和分享您的个人信息，以及您如何可以控制这些信息。
      </p>

      <h2>信息收集和使用</h2>
      <p>当您使用paperai时，我们可能会要求您提供个人识别信息，包括但不限于：</p>
      <ul>
        <li>电子邮件地址</li>
        <li>登录信息（如用户名和密码）</li>
      </ul>
      <p>此外，我们可能会自动收集关于您使用我们服务的某些信息，包括：</p>
      <ul>
        <li>设备信息</li>
        <li>日志信息</li>
        <li>使用数据</li>
        <li>Cookie</li>
      </ul>
      <p>我们使用收集到的信息来提供和改进服务，包括：</p>
      <ul>
        <li>提供定制的写作建议</li>
        <li>改进我们的寻找文献功能</li>
        <li>管理和维护服务</li>
        <li>通信</li>
        <li>数据分析和研究</li>
      </ul>

      <h2>数据分享和披露</h2>
      <p>
        我们不会将您的个人信息出售给第三方。在以下情况下，我们可能会共享您的信息：
      </p>
      <ul>
        <li>与信任的第三方服务提供者共享，以便他们代表我们提供服务</li>
        <li>为了遵守法律要求，包括应对法律程序、法院命令或政府要求</li>
        <li>在出售、合并、重组或其他公司交易中，作为交易的一部分</li>
      </ul>

      <h2>数据存储和安全</h2>
      <p>
        我们采取适当的物理、技术和管理措施来保护您的个人信息不被未经授权的访问、披露、修改或销毁。然而，互联网传输永远不可能是完全安全的，因此我们不能保证信息的绝对安全。
      </p>

      <h2>您的权利</h2>
      <p>
        您有权访问、更正、删除您的个人信息，以及在某些情况下限制或反对我们处理您的信息。如果您希望行使这些权利，请联系我们。
      </p>

      <h2>国际数据传输</h2>
      <p>
        您的信息可能会传输到并在您所在国家/地区以外的计算机上存储和处理，这些计算机的数据保护法律可能与您所在国家/地区的法律不同。
      </p>

      <h2>隐私政策的变更</h2>
      <p>
        我们可能会不时更新本隐私政策。我们将通过在paperai上发布新的隐私政策来通知您任何更改，并更新“最后更新”日期。
      </p>
      <h2>联系我们</h2>
      <p>
        如果您对本隐私政策有任何疑问，请通***********************过与我们联系。
      </p>

      <p>最后更新日期：2024.3.7</p>
    </div>
  );
}
