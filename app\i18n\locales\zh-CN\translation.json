{"give me a star in GitHub": "在GitHub上给我一颗star", "更新索引": "更新索引", "AI写作": "AI写作", "Paper2AI": "寻找文献", "点击AI写作就是正常的对话交流，点击寻找文献会根据输入的主题词去寻找对应论文": "点击AI写作就是正常的对话交流，点击寻找文献会根据输入的主题词去寻找对应论文", "选择论文来源": "选择论文来源", "选择AI模型": "选择AI模型", "生成轮数": "生成轮数", "时间范围": "文献发布日期范围，从这个时间到今年", "更新文中的上标，使得数字顺序排列": "更新文中的上标，使得数字顺序排列", "停止生成": "停止生成", "+ Add Paper": "+ 添加新论文（会直接替换编辑器里的内容）", "Buy VIP TO UNLOCK Cloud Sync and Edit Mutiple Papers Simultaneously": "购买VIP解锁云同步和同时编辑多篇论文", "Paper Management": "论文管理", "Your Cloud Papers": "您的云端论文", "复制": "复制", "添加自定义引用": "添加自定义引用", "复制所有引用": "复制所有引用", "删除所有引用": "删除所有引用", "Title": "标题", "Author": "作者", "Year": "年份", "Publisher": "出版商", "Url": "论文网址", "配置选择器": "配置选择器", "Upstream URL:": "请求模型的URL:", "System Prompt(Paper2AI):": "系统提示(Paper2AI):", "configurations": {"cocopilot-gpt4": "cocopilot-gpt4（apiKey前面手动加上ghu，因为GitHub不允许上传完整的密钥）", "deepseek-chat": "deepseek-chat（需要手动修改模型为这个）", "caifree": "<PERSON><PERSON><PERSON>（推荐）", "linuxdo": "linuxdo", "coze": "扣子coze(我亲自维护)", "官网反代": "官网反代", "vv佬": "vv佬(推荐)", "蒙恬大将军": "蒙恬大将军(推荐)", "oneapi": "oneapi", "custom": "自定义"}, "鼠标点击段落中的上标跳转到文献引用？": "鼠标点击段落中的上标跳转到文献引用？", "是否检查文献与主题相关性(如果不相关则不会传给AI引用)": "是否检查文献与主题相关性（如果不相关则不会传给AI引用）"}